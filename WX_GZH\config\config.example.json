{"gemini": {"api_key": "AIzaSyA_zK3tYdnCf6By1cPB76h-n1WCHs4OT-I", "model": "gemini-2.0-flash-exp", "base_url": "https://generativelanguage.googleapis.com/v1beta/models", "temperature": 0.7, "max_tokens": 2048}, "wechat": {"app_id": "您的微信公众号AppID", "app_secret": "您的微信公众号AppSecret", "token": "您的Token（可选）", "encoding_aes_key": "您的EncodingAESKey（可选）"}, "content": {"default_author": "您的名字", "default_tags": ["科技", "观点", "分享"], "max_article_length": 1500, "min_article_length": 500, "image_quality": "high", "auto_extract_images": true}, "database": {"type": "sqlite", "path": "data/articles.db", "backup_enabled": true, "backup_interval": "daily"}, "fetcher": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "timeout": 30, "retry_times": 3, "supported_platforms": ["youtube.com", "youtu.be", "bilibili.com", "zhihu.com", "mp.weixin.qq.com"]}, "ai": {"analysis_prompt_template": "content_analysis", "rewrite_prompt_template": "content_rewrite", "title_generation_template": "title_generation", "enable_fact_check": true, "enable_sentiment_analysis": true}, "publisher": {"auto_publish": false, "schedule_enabled": true, "default_publish_time": "09:00", "image_upload_enabled": true, "preview_before_publish": true}, "logging": {"level": "INFO", "file_path": "logs/app.log", "max_file_size": "10MB", "backup_count": 5}, "security": {"enable_rate_limiting": true, "max_requests_per_hour": 100, "enable_content_filtering": true, "blocked_keywords": ["敏感词1", "敏感词2"]}}