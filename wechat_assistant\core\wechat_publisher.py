"""
微信公众号发布器模块
处理文章发布到微信公众号的功能
"""

import json
import requests
import time
from typing import Dict, Optional, List
from dataclasses import dataclass
from loguru import logger
import hashlib
import os
from urllib.parse import urljoin


@dataclass
class PublishResult:
    """发布结果数据类"""
    success: bool
    message: str
    article_url: Optional[str] = None
    media_id: Optional[str] = None
    msg_id: Optional[str] = None
    error_code: Optional[int] = None


@dataclass
class WeChatArticle:
    """微信文章数据类"""
    title: str
    content: str
    author: str
    digest: str  # 摘要
    thumb_media_id: str  # 封面图片media_id
    show_cover_pic: int = 1  # 是否显示封面，0为false，1为true
    need_open_comment: int = 0  # 是否打开评论，0不打开，1打开
    only_fans_can_comment: int = 0  # 是否粉丝才可评论，0所有人可评论，1粉丝才可评论
    content_source_url: Optional[str] = None  # 原文链接


class WeChatPublisher:
    """微信公众号发布器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.app_id = config['wechat']['app_id']
        self.app_secret = config['wechat']['app_secret']
        self.access_token = None
        self.token_expires_at = 0
        
        # API 基础URL
        self.base_url = "https://api.weixin.qq.com/cgi-bin"
        
        # 发布配置
        self.auto_publish = config['publisher'].get('auto_publish', False)
        self.preview_before_publish = config['publisher'].get('preview_before_publish', True)
        
    def get_access_token(self) -> Optional[str]:
        """获取访问令牌"""
        try:
            # 检查token是否还有效
            if self.access_token and time.time() < self.token_expires_at:
                return self.access_token
            
            # 获取新的access_token
            url = f"{self.base_url}/token"
            params = {
                'grant_type': 'client_credential',
                'appid': self.app_id,
                'secret': self.app_secret
            }
            
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            
            if 'access_token' in result:
                self.access_token = result['access_token']
                # 提前5分钟过期，确保安全
                self.token_expires_at = time.time() + result.get('expires_in', 7200) - 300
                logger.info("成功获取access_token")
                return self.access_token
            else:
                logger.error(f"获取access_token失败: {result}")
                return None
                
        except Exception as e:
            logger.error(f"获取access_token异常: {str(e)}")
            return None
    
    def upload_media(self, file_path: str, media_type: str = 'image') -> Optional[str]:
        """
        上传多媒体文件
        
        Args:
            file_path: 文件路径
            media_type: 媒体类型 (image, voice, video, thumb)
            
        Returns:
            media_id或None
        """
        try:
            access_token = self.get_access_token()
            if not access_token:
                return None
            
            if not os.path.exists(file_path):
                logger.error(f"文件不存在: {file_path}")
                return None
            
            url = f"{self.base_url}/media/upload"
            params = {
                'access_token': access_token,
                'type': media_type
            }
            
            with open(file_path, 'rb') as f:
                files = {'media': f}
                response = requests.post(url, params=params, files=files, timeout=60)
                response.raise_for_status()
                
                result = response.json()
                
                if 'media_id' in result:
                    logger.info(f"成功上传媒体文件: {result['media_id']}")
                    return result['media_id']
                else:
                    logger.error(f"上传媒体文件失败: {result}")
                    return None
                    
        except Exception as e:
            logger.error(f"上传媒体文件异常: {str(e)}")
            return None
    
    def upload_permanent_media(self, file_path: str, media_type: str = 'image') -> Optional[str]:
        """
        上传永久素材
        
        Args:
            file_path: 文件路径
            media_type: 媒体类型
            
        Returns:
            media_id或None
        """
        try:
            access_token = self.get_access_token()
            if not access_token:
                return None
            
            if not os.path.exists(file_path):
                logger.error(f"文件不存在: {file_path}")
                return None
            
            url = f"{self.base_url}/material/add_material"
            params = {
                'access_token': access_token,
                'type': media_type
            }
            
            with open(file_path, 'rb') as f:
                files = {'media': f}
                response = requests.post(url, params=params, files=files, timeout=60)
                response.raise_for_status()
                
                result = response.json()
                
                if 'media_id' in result:
                    logger.info(f"成功上传永久素材: {result['media_id']}")
                    return result['media_id']
                else:
                    logger.error(f"上传永久素材失败: {result}")
                    return None
                    
        except Exception as e:
            logger.error(f"上传永久素材异常: {str(e)}")
            return None
    
    def create_draft(self, article: WeChatArticle) -> Optional[str]:
        """
        创建草稿
        
        Args:
            article: 文章对象
            
        Returns:
            media_id或None
        """
        try:
            access_token = self.get_access_token()
            if not access_token:
                return None
            
            url = f"{self.base_url}/draft/add"
            params = {'access_token': access_token}
            
            # 构建文章数据
            articles_data = {
                "articles": [
                    {
                        "title": article.title,
                        "author": article.author,
                        "digest": article.digest,
                        "content": article.content,
                        "content_source_url": article.content_source_url or "",
                        "thumb_media_id": article.thumb_media_id,
                        "show_cover_pic": article.show_cover_pic,
                        "need_open_comment": article.need_open_comment,
                        "only_fans_can_comment": article.only_fans_can_comment
                    }
                ]
            }
            
            headers = {'Content-Type': 'application/json; charset=utf-8'}
            
            response = requests.post(
                url, 
                params=params, 
                headers=headers,
                data=json.dumps(articles_data, ensure_ascii=False).encode('utf-8'),
                timeout=60
            )
            response.raise_for_status()
            
            result = response.json()
            
            if 'media_id' in result:
                logger.info(f"成功创建草稿: {result['media_id']}")
                return result['media_id']
            else:
                logger.error(f"创建草稿失败: {result}")
                return None
                
        except Exception as e:
            logger.error(f"创建草稿异常: {str(e)}")
            return None
    
    def publish_draft(self, media_id: str) -> PublishResult:
        """
        发布草稿
        
        Args:
            media_id: 草稿的media_id
            
        Returns:
            PublishResult对象
        """
        try:
            access_token = self.get_access_token()
            if not access_token:
                return PublishResult(
                    success=False,
                    message="获取access_token失败"
                )
            
            url = f"{self.base_url}/freepublish/submit"
            params = {'access_token': access_token}
            
            data = {"media_id": media_id}
            headers = {'Content-Type': 'application/json; charset=utf-8'}
            
            response = requests.post(
                url,
                params=params,
                headers=headers,
                data=json.dumps(data, ensure_ascii=False).encode('utf-8'),
                timeout=60
            )
            response.raise_for_status()
            
            result = response.json()
            
            if result.get('errcode', 0) == 0:
                publish_id = result.get('publish_id', '')
                logger.info(f"成功发布文章: {publish_id}")
                return PublishResult(
                    success=True,
                    message="发布成功",
                    msg_id=publish_id
                )
            else:
                error_msg = result.get('errmsg', '未知错误')
                logger.error(f"发布失败: {error_msg}")
                return PublishResult(
                    success=False,
                    message=error_msg,
                    error_code=result.get('errcode')
                )
                
        except Exception as e:
            logger.error(f"发布异常: {str(e)}")
            return PublishResult(
                success=False,
                message=f"发布异常: {str(e)}"
            )
    
    def get_publish_status(self, publish_id: str) -> Dict:
        """
        获取发布状态
        
        Args:
            publish_id: 发布任务ID
            
        Returns:
            状态信息字典
        """
        try:
            access_token = self.get_access_token()
            if not access_token:
                return {"error": "获取access_token失败"}
            
            url = f"{self.base_url}/freepublish/get"
            params = {'access_token': access_token}
            
            data = {"publish_id": publish_id}
            headers = {'Content-Type': 'application/json; charset=utf-8'}
            
            response = requests.post(
                url,
                params=params,
                headers=headers,
                data=json.dumps(data, ensure_ascii=False).encode('utf-8'),
                timeout=30
            )
            response.raise_for_status()
            
            result = response.json()
            return result
            
        except Exception as e:
            logger.error(f"获取发布状态异常: {str(e)}")
            return {"error": str(e)}
    
    def publish_article(self, article: WeChatArticle, thumb_image_path: Optional[str] = None) -> PublishResult:
        """
        完整的文章发布流程
        
        Args:
            article: 文章对象
            thumb_image_path: 封面图片路径
            
        Returns:
            PublishResult对象
        """
        try:
            # 1. 上传封面图片
            if thumb_image_path and os.path.exists(thumb_image_path):
                thumb_media_id = self.upload_permanent_media(thumb_image_path, 'image')
                if thumb_media_id:
                    article.thumb_media_id = thumb_media_id
                else:
                    logger.warning("封面图片上传失败，使用默认图片")
            
            # 2. 创建草稿
            draft_media_id = self.create_draft(article)
            if not draft_media_id:
                return PublishResult(
                    success=False,
                    message="创建草稿失败"
                )
            
            # 3. 如果启用预览，先不发布
            if self.preview_before_publish and not self.auto_publish:
                return PublishResult(
                    success=True,
                    message="草稿创建成功，等待手动发布",
                    media_id=draft_media_id
                )
            
            # 4. 发布草稿
            return self.publish_draft(draft_media_id)
            
        except Exception as e:
            logger.error(f"发布文章异常: {str(e)}")
            return PublishResult(
                success=False,
                message=f"发布异常: {str(e)}"
            )
