param(
    [string]$ApiKey = "AIzaSyA_zK3tYdnCf6By1cPB76h-n1WCHs4OT-I",
    [string]$Model = "gemini-1.5-flash",
    [switch]$ShowTokens = $true
)

# Set console encoding to UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::InputEncoding = [System.Text.Encoding]::UTF8

$uri = "https://generativelanguage.googleapis.com/v1beta/models/${Model}:generateContent?key=$ApiKey"

# Conversation history for context
$conversationHistory = @()

function Show-Welcome {
    Clear-Host
    Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Cyan
    Write-Host "║                    Gemini Interactive Chat                   ║" -ForegroundColor Cyan
    Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Commands:" -ForegroundColor Yellow
    Write-Host "  exit/quit    - End the session" -ForegroundColor Gray
    Write-Host "  clear        - Clear the screen" -ForegroundColor Gray
    Write-Host "  history      - Show conversation history" -ForegroundColor Gray
    Write-Host "  reset        - Reset conversation context" -ForegroundColor Gray
    Write-Host "  model        - Show current model" -ForegroundColor Gray
    Write-Host "  tokens       - Toggle token display" -ForegroundColor Gray
    Write-Host ""
    Write-Host "Current Model: $Model" -ForegroundColor Magenta
    Write-Host "Token Display: $(if($ShowTokens){'ON'}else{'OFF'})" -ForegroundColor Magenta
    Write-Host ""
}

Show-Welcome

while ($true) {
    Write-Host "You: " -ForegroundColor Green -NoNewline
    $message = Read-Host

    # Handle commands
    switch ($message.ToLower()) {
        { $_ -eq "exit" -or $_ -eq "quit" } {
            Write-Host "Goodbye! 👋" -ForegroundColor Cyan
            return
        }
        "clear" {
            Show-Welcome
            continue
        }
        "history" {
            Write-Host "`nConversation History:" -ForegroundColor Yellow
            for ($i = 0; $i -lt $conversationHistory.Count; $i++) {
                $entry = $conversationHistory[$i]
                Write-Host "[$($i+1)] You: " -ForegroundColor Green -NoNewline
                Write-Host $entry.user -ForegroundColor White
                Write-Host "[$($i+1)] Gemini: " -ForegroundColor Blue -NoNewline
                Write-Host $entry.assistant -ForegroundColor White
                Write-Host ""
            }
            continue
        }
        "reset" {
            $conversationHistory = @()
            Write-Host "Conversation context reset! 🔄" -ForegroundColor Yellow
            Write-Host ""
            continue
        }
        "model" {
            Write-Host "Current Model: $Model" -ForegroundColor Magenta
            Write-Host ""
            continue
        }
        "tokens" {
            $ShowTokens = -not $ShowTokens
            Write-Host "Token Display: $(if($ShowTokens){'ON'}else{'OFF'})" -ForegroundColor Magenta
            Write-Host ""
            continue
        }
    }

    if ([string]::IsNullOrWhiteSpace($message)) {
        continue
    }
    
    # Build conversation context (include recent history for better context)
    $contents = @()

    # Add recent conversation history (last 5 exchanges to maintain context)
    $recentHistory = $conversationHistory | Select-Object -Last 5
    foreach ($entry in $recentHistory) {
        $contents += @{
            role = "user"
            parts = @(@{ text = $entry.user })
        }
        $contents += @{
            role = "model"
            parts = @(@{ text = $entry.assistant })
        }
    }

    # Add current message
    $contents += @{
        role = "user"
        parts = @(@{ text = $message })
    }

    $body = @{
        contents = $contents
    } | ConvertTo-Json -Depth 10

    # Ensure UTF-8 encoding
    $body = [System.Text.Encoding]::UTF8.GetBytes($body)

    try {
        Write-Host "Gemini: " -ForegroundColor Blue -NoNewline
        Write-Host "🤔 " -NoNewline

        $response = Invoke-RestMethod -Uri $uri -Method Post -Headers @{"Content-Type"="application/json; charset=utf-8"} -Body $body

        if ($response.candidates -and $response.candidates.Count -gt 0) {
            $content = $response.candidates[0].content
            if ($content.parts -and $content.parts.Count -gt 0) {
                $assistantResponse = $content.parts[0].text
                Write-Host "`r" -NoNewline  # Clear the thinking emoji
                Write-Host "Gemini: " -ForegroundColor Blue -NoNewline
                Write-Host $assistantResponse -ForegroundColor White

                # Add to conversation history
                $conversationHistory += @{
                    user = $message
                    assistant = $assistantResponse
                    timestamp = Get-Date
                }

            } else {
                Write-Host "`r" -NoNewline
                Write-Host "Gemini: " -ForegroundColor Blue -NoNewline
                Write-Host "No response content found" -ForegroundColor Yellow
            }
        } else {
            Write-Host "`r" -NoNewline
            Write-Host "Gemini: " -ForegroundColor Blue -NoNewline
            Write-Host "No candidates found in response" -ForegroundColor Yellow
        }

        if ($ShowTokens -and $response.usageMetadata) {
            Write-Host "💬 Tokens: $($response.usageMetadata.promptTokenCount)→$($response.usageMetadata.candidatesTokenCount) (Total: $($response.usageMetadata.totalTokenCount))" -ForegroundColor DarkGray
        }
        Write-Host ""

    } catch {
        Write-Host "`r" -NoNewline
        Write-Host "Gemini: " -ForegroundColor Blue -NoNewline
        Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host ""
    }
}
