param(
    [string]$ApiKey = "AIzaSyA_zK3tYdnCf6By1cPB76h-n1WCHs4OT-I"
)

$uri = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=$ApiKey"

Write-Host "=== Gemini Interactive Chat ===" -ForegroundColor Cyan
Write-Host "Type 'exit' or 'quit' to end the session" -ForegroundColor Yellow
Write-Host "Type 'clear' to clear the screen" -ForegroundColor Yellow
Write-Host ""

while ($true) {
    Write-Host "You: " -ForegroundColor Green -NoNewline
    $message = Read-Host
    
    if ($message -eq "exit" -or $message -eq "quit") {
        Write-Host "Goodbye!" -ForegroundColor Cyan
        break
    }
    
    if ($message -eq "clear") {
        Clear-Host
        Write-Host "=== Gemini Interactive Chat ===" -ForegroundColor Cyan
        Write-Host "Type 'exit' or 'quit' to end the session" -ForegroundColor Yellow
        Write-Host "Type 'clear' to clear the screen" -ForegroundColor Yellow
        Write-Host ""
        continue
    }
    
    if ([string]::IsNullOrWhiteSpace($message)) {
        continue
    }
    
    $body = @{
        contents = @(
            @{
                parts = @(
                    @{
                        text = $message
                    }
                )
            }
        )
    } | ConvertTo-Json -Depth 10
    
    # Ensure UTF-8 encoding
    $body = [System.Text.Encoding]::UTF8.GetBytes($body)
    
    try {
        Write-Host "Gemini: " -ForegroundColor Blue -NoNewline
        $response = Invoke-RestMethod -Uri $uri -Method Post -Headers @{"Content-Type"="application/json; charset=utf-8"} -Body $body
        
        if ($response.candidates -and $response.candidates.Count -gt 0) {
            $content = $response.candidates[0].content
            if ($content.parts -and $content.parts.Count -gt 0) {
                Write-Host $content.parts[0].text -ForegroundColor White
            } else {
                Write-Host "No response content found" -ForegroundColor Yellow
            }
        } else {
            Write-Host "No candidates found in response" -ForegroundColor Yellow
        }
        
        Write-Host "Tokens: $($response.usageMetadata.promptTokenCount)→$($response.usageMetadata.candidatesTokenCount)" -ForegroundColor DarkGray
        Write-Host ""
        
    } catch {
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host ""
    }
}
