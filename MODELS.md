# Gemini 模型使用指南

## 支持的模型

### 1. Gemini 1.5 系列（稳定版）

| 别名 | 完整模型名 | 描述 | 推荐用途 |
|------|------------|------|----------|
| `flash` | `gemini-1.5-flash` | 快速响应，平衡性能 | 日常对话、快速问答 |
| `pro` | `gemini-1.5-pro` | 高性能，复杂推理 | 复杂分析、编程、创作 |
| `flash-8b` | `gemini-1.5-flash-8b` | 轻量级，快速 | 简单任务、批量处理 |

### 2. Gemini 2.0 系列（实验版）

| 别名 | 完整模型名 | 描述 | 推荐用途 |
|------|------------|------|----------|
| `2.0` | `gemini-2.0-flash-exp` | 最新实验版本 | 体验最新功能 |
| `2.0-flash` | `gemini-2.0-flash-exp` | 同上 | 体验最新功能 |
| `exp` | `gemini-2.0-flash-exp` | 同上 | 体验最新功能 |
| `experimental` | `gemini-2.0-flash-exp` | 同上 | 体验最新功能 |

## 使用方法

### 1. 单次查询

```powershell
# 使用别名
.\gemini-chat.ps1 -Message "你的问题" -Model "2.0"
.\gemini-chat.ps1 -Message "你的问题" -Model "exp"
.\gemini-chat.ps1 -Message "你的问题" -Model "pro"

# 使用完整模型名
.\gemini-chat.ps1 -Message "你的问题" -Model "gemini-2.0-flash-exp"
```

### 2. 交互式聊天

```powershell
# 启动时指定模型
.\gemini-interactive.ps1 -Model "2.0"
.\gemini-interactive.ps1 -Model "exp"
.\gemini-interactive.ps1 -Model "pro"
```

### 3. 批处理文件

```cmd
# 使用批处理文件（需要修改脚本指定模型）
gemini.bat "你的问题"
```

## 实际使用示例

### 使用 Gemini 2.0 Flash Experimental

```powershell
# 方式1：使用简短别名
.\gemini-chat.ps1 -Message "解释一下量子计算的基本原理" -Model "2.0"

# 方式2：使用 exp 别名
.\gemini-chat.ps1 -Message "写一个Python排序算法" -Model "exp"

# 方式3：使用完整名称
.\gemini-chat.ps1 -Message "翻译这段文字到英文：你好世界" -Model "gemini-2.0-flash-exp"
```

### 交互式使用

```powershell
# 启动 Gemini 2.0 交互式聊天
.\gemini-interactive.ps1 -Model "2.0"
```

## 模型特点对比

### Gemini 1.5 Flash vs 2.0 Flash Experimental

- **1.5 Flash**: 稳定可靠，适合生产环境
- **2.0 Flash Exp**: 最新功能，可能有更好的性能，但可能不够稳定

### 选择建议

1. **日常使用**: 推荐 `flash` (1.5-flash)
2. **复杂任务**: 推荐 `pro` (1.5-pro)  
3. **体验新功能**: 推荐 `2.0` 或 `exp` (2.0-flash-exp)
4. **快速简单任务**: 推荐 `flash-8b` (1.5-flash-8b)

## 注意事项

1. **实验版模型**可能会有以下限制：
   - 可能有速率限制
   - 响应可能不够稳定
   - 功能可能随时变化

2. **模型可用性**：
   - 某些模型可能在特定时间不可用
   - 如果遇到错误，可以尝试其他模型

3. **Token 使用**：
   - 不同模型的 token 消耗可能不同
   - 实验版模型可能有不同的计费规则

## 故障排除

如果某个模型不可用，尝试以下步骤：

1. 检查模型名称是否正确
2. 尝试使用其他模型
3. 等待一段时间后重试
4. 检查网络连接

## 更新模型列表

要添加新的模型支持，需要更新以下文件：

1. `gemini-config.json` - 添加模型别名
2. `gemini-chat.ps1` - 更新模型映射
3. `gemini-interactive.ps1` - 更新模型映射
