# 微信公众号自动发布助手

一个智能的微信公众号内容创作和发布工具，帮助您将有趣的视频和文章转化为高质量的公众号内容。

## 🌟 功能特色

- **智能内容抓取**：支持 YouTube、B站、知乎、微信文章等多种内容源
- **AI 内容分析**：使用 Gemini AI 进行内容理解和观点生成
- **人工审核**：Web 界面预览和编辑 AI 生成的内容
- **自动发布**：一键发布到微信公众号
- **定时发布**：支持预约发布功能

## 🏗️ 系统架构

```
内容输入 → 内容抓取 → AI分析 → 生成草稿 → 人工审核 → 自动发布
```

## 🛠️ 技术栈

- **后端**：Python 3.9+, FastAPI
- **前端**：Streamlit (快速原型)
- **AI引擎**：Gemini API
- **内容抓取**：yt-dlp, BeautifulSoup4, requests
- **微信API**：微信公众平台 API
- **数据库**：SQLite (可升级到 PostgreSQL)

## 📦 安装和配置

### 1. 环境要求

```bash
Python 3.9+
pip
```

### 2. 安装依赖

```bash
cd wechat_assistant
pip install -r requirements.txt
```

### 3. 配置文件

复制配置模板并填入您的 API 密钥：

```bash
cp config/config.example.json config/config.json
```

编辑 `config/config.json`：

```json
{
  "gemini": {
    "api_key": "您的Gemini API Key",
    "model": "gemini-2.0-flash-exp"
  },
  "wechat": {
    "app_id": "您的微信公众号AppID",
    "app_secret": "您的微信公众号AppSecret"
  },
  "content": {
    "default_author": "您的名字",
    "default_tags": ["科技", "观点", "分享"]
  }
}
```

## 🚀 快速开始

### 1. 启动 Web 界面

```bash
python -m streamlit run app/main.py
```

### 2. 使用命令行工具

```bash
# 分析单个链接
python cli.py analyze "https://www.youtube.com/watch?v=example"

# 批量处理
python cli.py batch process_list.txt
```

## 📖 使用指南

### 1. 添加内容源

支持的内容类型：
- YouTube 视频链接
- B站视频链接  
- 知乎文章链接
- 微信公众号文章链接
- 其他网页文章链接

### 2. AI 分析和生成

系统会自动：
- 提取内容核心信息
- 生成个人观点和见解
- 优化文章结构和语言
- 生成吸引人的标题

### 3. 人工审核

在 Web 界面中：
- 预览生成的文章
- 编辑标题、正文、标签
- 调整发布时间
- 确认发布

### 4. 自动发布

- 自动格式化内容
- 上传相关图片
- 发布到微信公众号
- 记录发布状态

## 📁 项目结构

```
wechat_assistant/
├── app/                    # Web 应用
│   ├── main.py            # Streamlit 主应用
│   ├── pages/             # 页面组件
│   └── components/        # UI 组件
├── core/                  # 核心功能模块
│   ├── content_fetcher.py # 内容抓取器
│   ├── ai_analyzer.py     # AI 分析器
│   ├── wechat_publisher.py# 微信发布器
│   └── database.py        # 数据库操作
├── config/                # 配置文件
│   ├── config.json        # 主配置文件
│   └── prompts.py         # AI 提示词模板
├── utils/                 # 工具函数
├── tests/                 # 测试文件
├── requirements.txt       # 依赖包列表
├── cli.py                # 命令行工具
└── README.md             # 项目说明
```

## 🔧 配置说明

### Gemini API 配置

使用您已有的 Gemini API 配置：
- API Key: `AIzaSyA_zK3tYdnCf6By1cPB76h-n1WCHs4OT-I`
- 推荐模型: `gemini-2.0-flash-exp`

### 微信公众号配置

需要在微信公众平台申请：
1. 登录微信公众平台
2. 开发 → 基本配置
3. 获取 AppID 和 AppSecret
4. 配置服务器地址（如需要）

## 📝 开发计划

- [x] 项目架构设计
- [x] 技术方案调研
- [ ] 核心模块开发
- [ ] Web 界面开发
- [ ] 微信 API 集成
- [ ] 测试和优化

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
