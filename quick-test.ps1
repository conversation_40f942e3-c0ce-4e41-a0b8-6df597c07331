# Quick Gemini Test Script
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::InputEncoding = [System.Text.Encoding]::UTF8

Write-Host "🧪 Quick Gemini Test" -ForegroundColor Cyan
Write-Host ""

# Test 1: API connectivity
Write-Host "1️⃣ Testing API connectivity..." -ForegroundColor Yellow

$apiKey = "AIzaSyA_zK3tYdnCf6By1cPB76h-n1WCHs4OT-I"
$uri = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=$apiKey"

$body = @{
    contents = @(
        @{
            parts = @(
                @{
                    text = "Hello, respond with just 'OK' if you can understand me."
                }
            )
        }
    )
} | ConvertTo-Json -Depth 10

$bodyBytes = [System.Text.Encoding]::UTF8.GetBytes($body)

try {
    $headers = @{"Content-Type"="application/json; charset=utf-8"}
    $response = Invoke-RestMethod -Uri $uri -Method Post -Headers $headers -Body $bodyBytes
    if ($response.candidates -and $response.candidates.Count -gt 0) {
        Write-Host "   ✅ API connectivity: SUCCESS" -ForegroundColor Green
        $responseText = $response.candidates[0].content.parts[0].text.Trim()
        Write-Host "   📝 Response: $responseText" -ForegroundColor Gray
    } else {
        Write-Host "   ❌ API connectivity: FAILED (No response)" -ForegroundColor Red
        return
    }
} catch {
    Write-Host "   ❌ API connectivity: FAILED" -ForegroundColor Red
    Write-Host "   🔍 Error: $($_.Exception.Message)" -ForegroundColor Red
    return
}

Write-Host ""

# Test 2: Chinese support
Write-Host "2️⃣ Testing Chinese language support..." -ForegroundColor Yellow

$chineseBody = @{
    contents = @(
        @{
            parts = @(
                @{
                    text = "请用中文回答：你好吗？"
                }
            )
        }
    )
} | ConvertTo-Json -Depth 10

$chineseBodyBytes = [System.Text.Encoding]::UTF8.GetBytes($chineseBody)

try {
    $response = Invoke-RestMethod -Uri $uri -Method Post -Headers $headers -Body $chineseBodyBytes
    if ($response.candidates -and $response.candidates.Count -gt 0) {
        $chineseResponse = $response.candidates[0].content.parts[0].text.Trim()
        Write-Host "   ✅ Chinese support: SUCCESS" -ForegroundColor Green
        Write-Host "   📝 Response: $chineseResponse" -ForegroundColor Gray
    } else {
        Write-Host "   ❌ Chinese support: FAILED" -ForegroundColor Red
    }
} catch {
    Write-Host "   ❌ Chinese support: FAILED" -ForegroundColor Red
    Write-Host "   🔍 Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 3: Script files
Write-Host "3️⃣ Testing script files..." -ForegroundColor Yellow

$scripts = @(
    "gemini-chat.ps1",
    "gemini-interactive.ps1", 
    "gemini.bat",
    "gemini-config.json"
)

foreach ($script in $scripts) {
    if (Test-Path $script) {
        Write-Host "   ✅ ${script}: EXISTS" -ForegroundColor Green
    } else {
        Write-Host "   ❌ ${script}: MISSING" -ForegroundColor Red
    }
}

Write-Host ""

# Summary
Write-Host "🎯 Test Summary:" -ForegroundColor Cyan
Write-Host ""
Write-Host "✅ Your Gemini PowerShell client is ready to use!" -ForegroundColor Green
Write-Host ""
Write-Host "📖 Quick start commands:" -ForegroundColor Yellow
Write-Host "   .\gemini.bat                              # Simplest way" -ForegroundColor White
Write-Host "   .\gemini.bat 'Your question'              # Single question" -ForegroundColor White
Write-Host "   .\gemini-interactive.ps1                  # Interactive chat" -ForegroundColor White
Write-Host "   .\gemini-chat.ps1 -Message 'Your question' # Single message" -ForegroundColor White
Write-Host ""
