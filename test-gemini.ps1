# Gemini PowerShell Client Test Script
param(
    [switch]$Quick
)

# Set console encoding to UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::InputEncoding = [System.Text.Encoding]::UTF8

Write-Host "🧪 Testing Gemini PowerShell Client..." -ForegroundColor Cyan
Write-Host ""

# Test 1: Basic API connectivity
Write-Host "1️⃣ Testing API connectivity..." -ForegroundColor Yellow

$apiKey = "AIzaSyA_zK3tYdnCf6By1cPB76h-n1WCHs4OT-I"
$uri = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=$apiKey"

$body = @{
    contents = @(
        @{
            parts = @(
                @{
                    text = "Hello, respond with just 'OK' if you can understand me."
                }
            )
        }
    )
} | ConvertTo-Json -Depth 10

$body = [System.Text.Encoding]::UTF8.GetBytes($body)

try {
    $response = Invoke-RestMethod -Uri $uri -Method Post -Headers @{"Content-Type"="application/json; charset=utf-8"} -Body $body
    if ($response.candidates -and $response.candidates.Count -gt 0) {
        Write-Host "   ✅ API connectivity: SUCCESS" -ForegroundColor Green
        Write-Host "   📝 Response: $($response.candidates[0].content.parts[0].text.Trim())" -ForegroundColor Gray
    } else {
        Write-Host "   ❌ API connectivity: FAILED (No response)" -ForegroundColor Red
        return
    }
} catch {
    Write-Host "   ❌ API connectivity: FAILED" -ForegroundColor Red
    Write-Host "   🔍 Error: $($_.Exception.Message)" -ForegroundColor Red
    return
}

Write-Host ""

# Test 2: Chinese support
Write-Host "2️⃣ Testing Chinese language support..." -ForegroundColor Yellow

$body = @{
    contents = @(
        @{
            parts = @(
                @{
                    text = "请用中文回答：你好吗？"
                }
            )
        }
    )
} | ConvertTo-Json -Depth 10

$body = [System.Text.Encoding]::UTF8.GetBytes($body)

try {
    $response = Invoke-RestMethod -Uri $uri -Method Post -Headers @{"Content-Type"="application/json; charset=utf-8"} -Body $body
    if ($response.candidates -and $response.candidates.Count -gt 0) {
        $chineseResponse = $response.candidates[0].content.parts[0].text.Trim()
        Write-Host "   ✅ Chinese support: SUCCESS" -ForegroundColor Green
        Write-Host "   📝 Response: $chineseResponse" -ForegroundColor Gray
    } else {
        Write-Host "   ❌ Chinese support: FAILED" -ForegroundColor Red
    }
} catch {
    Write-Host "   ❌ Chinese support: FAILED" -ForegroundColor Red
    Write-Host "   🔍 Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 3: Script files
Write-Host "3️⃣ Testing script files..." -ForegroundColor Yellow

$scripts = @(
    "gemini-chat.ps1",
    "gemini-interactive.ps1", 
    "gemini-advanced.ps1",
    "start-gemini.ps1",
    "gemini.bat"
)

foreach ($script in $scripts) {
    if (Test-Path $script) {
        Write-Host "   ✅ $script: EXISTS" -ForegroundColor Green
    } else {
        Write-Host "   ❌ $script: MISSING" -ForegroundColor Red
    }
}

Write-Host ""

# Test 4: Configuration file
Write-Host "4️⃣ Testing configuration..." -ForegroundColor Yellow

if (Test-Path "gemini-config.json") {
    try {
        $config = Get-Content "gemini-config.json" -Raw | ConvertFrom-Json
        Write-Host "   ✅ Configuration file: VALID" -ForegroundColor Green
        Write-Host "   📝 Default model: $($config.defaultModel)" -ForegroundColor Gray
        Write-Host "   📝 Show tokens: $($config.settings.showTokens)" -ForegroundColor Gray
    } catch {
        Write-Host "   ❌ Configuration file: INVALID JSON" -ForegroundColor Red
    }
} else {
    Write-Host "   ⚠️  Configuration file: NOT FOUND (will use defaults)" -ForegroundColor Yellow
}

Write-Host ""

if (-not $Quick) {
    # Test 5: Interactive test
    Write-Host "5️⃣ Testing single message script..." -ForegroundColor Yellow
    
    try {
        Write-Host "   🔄 Running: .\gemini-chat.ps1 -Message 'Test message'" -ForegroundColor Gray
        $result = & ".\gemini-chat.ps1" -Message "Just respond with 'Script test successful'" 2>&1
        if ($LASTEXITCODE -eq 0 -or $result -match "successful") {
            Write-Host "   ✅ Single message script: SUCCESS" -ForegroundColor Green
        } else {
            Write-Host "   ❌ Single message script: FAILED" -ForegroundColor Red
            Write-Host "   🔍 Output: $result" -ForegroundColor Red
        }
    } catch {
        Write-Host "   ❌ Single message script: FAILED" -ForegroundColor Red
        Write-Host "   🔍 Error: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Write-Host ""
}

# Summary
Write-Host "🎯 Test Summary:" -ForegroundColor Cyan
Write-Host ""
Write-Host "✅ Your Gemini PowerShell client is ready to use!" -ForegroundColor Green
Write-Host ""
Write-Host "📖 Quick start commands:" -ForegroundColor Yellow
Write-Host "   .\gemini.bat                              # Simplest way (interactive)" -ForegroundColor White
Write-Host "   .\gemini.bat 'Your question'              # Single question" -ForegroundColor White
Write-Host "   .\gemini-interactive.ps1                  # Interactive chat" -ForegroundColor White
Write-Host "   .\gemini-chat.ps1 -Message 'Your question' # Single message" -ForegroundColor White
Write-Host ""
Write-Host "🎨 Available models:" -ForegroundColor Yellow
Write-Host "   flash    - Fast and efficient (default)" -ForegroundColor White
Write-Host "   pro      - More capable, slower" -ForegroundColor White
Write-Host "   flash-8b - Lightweight version" -ForegroundColor White
Write-Host ""
Write-Host "Example with specific model:" -ForegroundColor Yellow
Write-Host "   .\gemini-chat.ps1 -Message 'Complex question' -Model pro" -ForegroundColor White
Write-Host ""
