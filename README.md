# Gemini PowerShell Client

一个功能完整的 Google Gemini AI PowerShell 客户端，支持交互式对话和单次查询。

## 🚀 快速开始

### 最简单的使用方式

```bash
# 启动交互式聊天
.\gemini.bat

# 发送单条消息
.\gemini.bat "你好，请介绍一下自己"
```

### PowerShell 方式

```powershell
# 交互式聊天
.\gemini-interactive.ps1

# 单次查询
.\gemini-chat.ps1 -Message "什么是人工智能？"

# 使用不同模型
.\gemini-chat.ps1 -Message "复杂问题" -Model pro
```

## 📁 文件说明

| 文件 | 描述 |
|------|------|
| `gemini.bat` | 最简单的启动方式（推荐） |
| `gemini-interactive.ps1` | 交互式聊天脚本 |
| `gemini-chat.ps1` | 单次查询脚本 |
| `gemini-advanced.ps1` | 高级客户端（支持配置文件） |
| `start-gemini.ps1` | 统一启动器 |
| `gemini-config.json` | 配置文件 |

## 🎯 使用方法

### 1. 交互式聊天

```powershell
.\gemini-interactive.ps1
```

**交互式命令：**
- `exit` / `quit` - 退出程序
- `clear` - 清屏
- `history` - 显示对话历史
- `reset` - 重置对话上下文
- `model` - 显示当前模型
- `tokens` - 切换 token 显示

### 2. 单次查询

```powershell
# 基本用法
.\gemini-chat.ps1 -Message "你的问题"

# 指定模型
.\gemini-chat.ps1 -Message "复杂问题" -Model pro
```

### 3. 模型选择

支持的模型：
- `flash` / `gemini-1.5-flash` - 快速高效（默认）
- `pro` / `gemini-1.5-pro` - 更强能力，较慢
- `flash-8b` / `gemini-1.5-flash-8b` - 轻量版本

### 4. 统一启动器

```powershell
# 交互模式
.\start-gemini.ps1

# 单次消息
.\start-gemini.ps1 -Mode single -Message "你的问题"

# 使用特定模型
.\start-gemini.ps1 -Mode interactive -Model pro

# 显示帮助
.\start-gemini.ps1 -Mode help
```

## ⚙️ 配置

### API 密钥配置

API 密钥已预配置在脚本中。如需修改，可以：

1. **修改配置文件** `gemini-config.json`：
```json
{
  "apiKey": "你的API密钥"
}
```

2. **直接传参**：
```powershell
.\gemini-chat.ps1 -Message "问题" -ApiKey "你的API密钥"
```

### 高级配置

编辑 `gemini-config.json` 文件：

```json
{
  "apiKey": "你的API密钥",
  "defaultModel": "gemini-1.5-flash",
  "settings": {
    "showTokens": true,
    "maxHistoryEntries": 10,
    "temperature": 0.7,
    "maxOutputTokens": 2048
  }
}
```

## 🎨 功能特性

### ✨ 交互式聊天特性
- 🔄 保持对话上下文
- 📊 实时 token 使用统计
- 🎨 彩色输出界面
- 📝 对话历史记录
- 🔧 多种实用命令

### 🚀 单次查询特性
- ⚡ 快速响应
- 🎯 简洁输出
- 📊 Token 使用统计
- 🔧 模型选择

### 🛠️ 技术特性
- 🌐 UTF-8 编码支持（完美支持中文）
- 🔒 错误处理和重试机制
- 📱 跨平台 PowerShell 支持
- ⚙️ 灵活的配置系统

## 📖 使用示例

### 日常对话
```bash
.\gemini.bat
You: 你好，请介绍一下自己
Gemini: 你好！我是 Gemini，Google 开发的大型语言模型...
```

### 代码帮助
```bash
.\gemini.bat "用 Python 写一个快速排序算法"
```

### 翻译
```bash
.\gemini.bat "把'Hello World'翻译成中文"
```

### 复杂问题（使用 Pro 模型）
```powershell
.\gemini-chat.ps1 -Message "解释量子计算的基本原理" -Model pro
```

## 🔧 故障排除

### 常见问题

1. **编码问题**：脚本已自动设置 UTF-8 编码
2. **网络问题**：确保能访问 `generativelanguage.googleapis.com`
3. **API 密钥问题**：检查密钥是否正确配置

### 执行策略问题

如果遇到执行策略限制：
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

## 📝 更新日志

- ✅ 支持多模型选择
- ✅ 交互式对话上下文
- ✅ 配置文件支持
- ✅ 彩色界面和表情符号
- ✅ Token 使用统计
- ✅ 批处理文件支持

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
