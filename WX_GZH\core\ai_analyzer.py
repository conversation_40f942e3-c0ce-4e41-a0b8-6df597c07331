"""
AI 内容分析器模块
使用 Gemini API 进行内容分析和文章生成
"""

import json
import requests
from typing import Dict, Optional, List
from dataclasses import dataclass
from loguru import logger
import sys
import os

# 添加配置路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from config.prompts import format_prompt
from core.content_fetcher import ContentInfo


@dataclass
class AnalysisResult:
    """AI 分析结果数据类"""
    title: str
    summary: str
    content: str
    tags: List[str]
    image_suggestions: str
    quality_score: float
    original_content: ContentInfo
    analysis_metadata: Dict = None

    def __post_init__(self):
        if self.analysis_metadata is None:
            self.analysis_metadata = {}


class GeminiAnalyzer:
    """Gemini AI 分析器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.api_key = config['gemini']['api_key']
        self.model = config['gemini']['model']
        self.base_url = config['gemini']['base_url']
        self.temperature = config['gemini'].get('temperature', 0.7)
        self.max_tokens = config['gemini'].get('max_tokens', 2048)
        
        # 内容配置
        self.min_length = config['content'].get('min_article_length', 500)
        self.max_length = config['content'].get('max_article_length', 1500)
        self.default_author = config['content'].get('default_author', '')
        
    def analyze_content(self, content_info: ContentInfo) -> Optional[AnalysisResult]:
        """
        分析内容并生成文章
        
        Args:
            content_info: 原始内容信息
            
        Returns:
            AnalysisResult对象或None
        """
        try:
            logger.info(f"开始分析内容: {content_info.title}")
            
            # 生成文章内容
            article_result = self._generate_article(content_info)
            if not article_result:
                return None
            
            # 解析生成的内容
            parsed_result = self._parse_article_result(article_result)
            if not parsed_result:
                return None
            
            # 质量检查
            quality_score = self._check_quality(parsed_result)
            
            return AnalysisResult(
                title=parsed_result.get('title', ''),
                summary=parsed_result.get('summary', ''),
                content=parsed_result.get('content', ''),
                tags=parsed_result.get('tags', []),
                image_suggestions=parsed_result.get('image_suggestions', ''),
                quality_score=quality_score,
                original_content=content_info,
                analysis_metadata={
                    'model_used': self.model,
                    'content_length': len(parsed_result.get('content', '')),
                    'processing_time': None  # 可以添加时间统计
                }
            )
            
        except Exception as e:
            logger.error(f"内容分析失败: {str(e)}")
            return None
    
    def _generate_article(self, content_info: ContentInfo) -> Optional[str]:
        """使用 Gemini API 生成文章"""
        try:
            # 构建提示词
            prompt = format_prompt(
                'content_analysis',
                content_type=content_info.content_type,
                content=content_info.content[:4000],  # 限制输入长度
                min_length=self.min_length,
                max_length=self.max_length
            )
            
            # 调用 Gemini API
            response = self._call_gemini_api(prompt)
            return response
            
        except Exception as e:
            logger.error(f"生成文章失败: {str(e)}")
            return None
    
    def _call_gemini_api(self, prompt: str) -> Optional[str]:
        """调用 Gemini API"""
        try:
            url = f"{self.base_url}/{self.model}:generateContent?key={self.api_key}"
            
            payload = {
                "contents": [
                    {
                        "parts": [
                            {
                                "text": prompt
                            }
                        ]
                    }
                ],
                "generationConfig": {
                    "temperature": self.temperature,
                    "maxOutputTokens": self.max_tokens,
                }
            }
            
            headers = {
                "Content-Type": "application/json; charset=utf-8"
            }
            
            response = requests.post(
                url, 
                headers=headers, 
                data=json.dumps(payload, ensure_ascii=False).encode('utf-8'),
                timeout=60
            )
            
            response.raise_for_status()
            result = response.json()
            
            if 'candidates' in result and len(result['candidates']) > 0:
                content = result['candidates'][0]['content']['parts'][0]['text']
                
                # 记录token使用情况
                if 'usageMetadata' in result:
                    usage = result['usageMetadata']
                    logger.info(f"Token使用: 输入={usage.get('promptTokenCount', 0)}, "
                              f"输出={usage.get('candidatesTokenCount', 0)}, "
                              f"总计={usage.get('totalTokenCount', 0)}")
                
                return content.strip()
            else:
                logger.error("API响应中没有有效内容")
                return None
                
        except requests.exceptions.RequestException as e:
            logger.error(f"API请求失败: {str(e)}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"调用Gemini API失败: {str(e)}")
            return None
    
    def _parse_article_result(self, result: str) -> Optional[Dict]:
        """解析AI生成的文章结果"""
        try:
            lines = result.split('\n')
            parsed = {
                'title': '',
                'summary': '',
                'content': '',
                'tags': [],
                'image_suggestions': ''
            }
            
            current_section = None
            content_lines = []
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                # 识别各个部分
                if line.startswith('标题：'):
                    parsed['title'] = line.replace('标题：', '').strip()
                elif line.startswith('摘要：'):
                    parsed['summary'] = line.replace('摘要：', '').strip()
                elif line.startswith('正文：'):
                    current_section = 'content'
                    continue
                elif line.startswith('标签：'):
                    tags_str = line.replace('标签：', '').strip()
                    parsed['tags'] = [tag.strip() for tag in tags_str.split(',') if tag.strip()]
                elif line.startswith('配图建议：'):
                    parsed['image_suggestions'] = line.replace('配图建议：', '').strip()
                elif current_section == 'content':
                    content_lines.append(line)
            
            # 组装正文内容
            if content_lines:
                parsed['content'] = '\n\n'.join(content_lines)
            
            # 验证必要字段
            if not parsed['title'] or not parsed['content']:
                logger.warning("解析结果缺少必要字段")
                return None
            
            return parsed
            
        except Exception as e:
            logger.error(f"解析文章结果失败: {str(e)}")
            return None
    
    def _check_quality(self, parsed_result: Dict) -> float:
        """检查文章质量并返回评分"""
        try:
            score = 0.0
            
            # 标题质量检查 (20分)
            title = parsed_result.get('title', '')
            if title:
                if 10 <= len(title) <= 30:
                    score += 20
                elif 5 <= len(title) <= 40:
                    score += 15
                else:
                    score += 10
            
            # 内容长度检查 (30分)
            content = parsed_result.get('content', '')
            content_length = len(content)
            if self.min_length <= content_length <= self.max_length:
                score += 30
            elif content_length >= self.min_length * 0.8:
                score += 25
            elif content_length >= self.min_length * 0.6:
                score += 20
            else:
                score += 10
            
            # 摘要质量检查 (20分)
            summary = parsed_result.get('summary', '')
            if summary:
                if 50 <= len(summary) <= 150:
                    score += 20
                elif 30 <= len(summary) <= 200:
                    score += 15
                else:
                    score += 10
            
            # 标签质量检查 (15分)
            tags = parsed_result.get('tags', [])
            if 3 <= len(tags) <= 6:
                score += 15
            elif 1 <= len(tags) <= 8:
                score += 10
            else:
                score += 5
            
            # 结构完整性检查 (15分)
            if all(key in parsed_result for key in ['title', 'summary', 'content', 'tags']):
                score += 15
            
            return min(score, 100.0)  # 确保不超过100分
            
        except Exception as e:
            logger.error(f"质量检查失败: {str(e)}")
            return 0.0
    
    def generate_alternative_titles(self, content: str, keywords: List[str]) -> List[str]:
        """生成备选标题"""
        try:
            prompt = format_prompt(
                'title_generation',
                summary=content[:200],
                keywords=', '.join(keywords)
            )
            
            response = self._call_gemini_api(prompt)
            if not response:
                return []
            
            # 解析标题列表
            lines = response.split('\n')
            titles = []
            
            for line in lines:
                line = line.strip()
                if line and (line.startswith(('1.', '2.', '3.', '4.', '5.')) or 
                           line.startswith('推荐标题：')):
                    # 提取标题内容
                    if line.startswith('推荐标题：'):
                        title = line.replace('推荐标题：', '').strip()
                    else:
                        title = line.split('.', 1)[1].strip() if '.' in line else line
                    
                    if title:
                        titles.append(title)
            
            return titles[:5]  # 返回最多5个标题
            
        except Exception as e:
            logger.error(f"生成备选标题失败: {str(e)}")
            return []
    
    def rewrite_content(self, original_content: str, target_length: int) -> Optional[str]:
        """重写和优化内容"""
        try:
            prompt = format_prompt(
                'content_rewrite',
                original_content=original_content,
                target_length=target_length
            )
            
            response = self._call_gemini_api(prompt)
            return response
            
        except Exception as e:
            logger.error(f"重写内容失败: {str(e)}")
            return None
