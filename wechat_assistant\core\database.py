"""
数据库操作模块
管理文章、分析结果和发布记录
"""

import sqlite3
import json
from datetime import datetime
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict
from loguru import logger
import os


@dataclass
class ArticleRecord:
    """文章记录数据类"""
    id: Optional[int] = None
    title: str = ""
    content: str = ""
    summary: str = ""
    tags: str = ""  # JSON字符串
    author: str = ""
    source_url: str = ""
    source_platform: str = ""
    content_type: str = ""
    quality_score: float = 0.0
    status: str = "draft"  # draft, published, rejected
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    published_at: Optional[str] = None
    wechat_media_id: Optional[str] = None
    wechat_article_url: Optional[str] = None
    original_content: str = ""  # JSON字符串，存储原始内容信息


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self._ensure_db_directory()
        self._init_database()
    
    def _ensure_db_directory(self):
        """确保数据库目录存在"""
        db_dir = os.path.dirname(self.db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir, exist_ok=True)
    
    def _init_database(self):
        """初始化数据库表"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 创建文章表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS articles (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        title TEXT NOT NULL,
                        content TEXT NOT NULL,
                        summary TEXT,
                        tags TEXT,
                        author TEXT,
                        source_url TEXT,
                        source_platform TEXT,
                        content_type TEXT,
                        quality_score REAL DEFAULT 0.0,
                        status TEXT DEFAULT 'draft',
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        published_at TEXT,
                        wechat_media_id TEXT,
                        wechat_article_url TEXT,
                        original_content TEXT
                    )
                ''')
                
                # 创建发布记录表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS publish_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        article_id INTEGER,
                        action TEXT NOT NULL,
                        status TEXT NOT NULL,
                        message TEXT,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (article_id) REFERENCES articles (id)
                    )
                ''')
                
                # 创建配置表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS settings (
                        key TEXT PRIMARY KEY,
                        value TEXT,
                        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                conn.commit()
                logger.info("数据库初始化完成")
                
        except Exception as e:
            logger.error(f"数据库初始化失败: {str(e)}")
            raise
    
    def save_article(self, article: ArticleRecord) -> Optional[int]:
        """保存文章记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                now = datetime.now().isoformat()
                
                if article.id:
                    # 更新现有记录
                    cursor.execute('''
                        UPDATE articles SET
                            title = ?, content = ?, summary = ?, tags = ?,
                            author = ?, source_url = ?, source_platform = ?,
                            content_type = ?, quality_score = ?, status = ?,
                            updated_at = ?, published_at = ?, wechat_media_id = ?,
                            wechat_article_url = ?, original_content = ?
                        WHERE id = ?
                    ''', (
                        article.title, article.content, article.summary, article.tags,
                        article.author, article.source_url, article.source_platform,
                        article.content_type, article.quality_score, article.status,
                        now, article.published_at, article.wechat_media_id,
                        article.wechat_article_url, article.original_content, article.id
                    ))
                    article_id = article.id
                else:
                    # 插入新记录
                    cursor.execute('''
                        INSERT INTO articles (
                            title, content, summary, tags, author, source_url,
                            source_platform, content_type, quality_score, status,
                            created_at, updated_at, wechat_media_id, wechat_article_url,
                            original_content
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        article.title, article.content, article.summary, article.tags,
                        article.author, article.source_url, article.source_platform,
                        article.content_type, article.quality_score, article.status,
                        now, now, article.wechat_media_id, article.wechat_article_url,
                        article.original_content
                    ))
                    article_id = cursor.lastrowid
                
                conn.commit()
                logger.info(f"文章保存成功: ID={article_id}")
                return article_id
                
        except Exception as e:
            logger.error(f"保存文章失败: {str(e)}")
            return None
    
    def get_article(self, article_id: int) -> Optional[ArticleRecord]:
        """获取文章记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute('SELECT * FROM articles WHERE id = ?', (article_id,))
                row = cursor.fetchone()
                
                if row:
                    return ArticleRecord(**dict(row))
                return None
                
        except Exception as e:
            logger.error(f"获取文章失败: {str(e)}")
            return None
    
    def get_articles(self, status: Optional[str] = None, limit: int = 50, offset: int = 0) -> List[ArticleRecord]:
        """获取文章列表"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                if status:
                    cursor.execute('''
                        SELECT * FROM articles WHERE status = ?
                        ORDER BY created_at DESC LIMIT ? OFFSET ?
                    ''', (status, limit, offset))
                else:
                    cursor.execute('''
                        SELECT * FROM articles
                        ORDER BY created_at DESC LIMIT ? OFFSET ?
                    ''', (limit, offset))
                
                rows = cursor.fetchall()
                return [ArticleRecord(**dict(row)) for row in rows]
                
        except Exception as e:
            logger.error(f"获取文章列表失败: {str(e)}")
            return []
    
    def update_article_status(self, article_id: int, status: str, wechat_media_id: Optional[str] = None) -> bool:
        """更新文章状态"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                now = datetime.now().isoformat()
                published_at = now if status == 'published' else None
                
                if wechat_media_id:
                    cursor.execute('''
                        UPDATE articles SET status = ?, updated_at = ?, 
                               published_at = ?, wechat_media_id = ?
                        WHERE id = ?
                    ''', (status, now, published_at, wechat_media_id, article_id))
                else:
                    cursor.execute('''
                        UPDATE articles SET status = ?, updated_at = ?, published_at = ?
                        WHERE id = ?
                    ''', (status, now, published_at, article_id))
                
                conn.commit()
                
                # 记录操作日志
                self.log_publish_action(article_id, f"status_change_to_{status}", "success", f"状态更新为: {status}")
                
                return True
                
        except Exception as e:
            logger.error(f"更新文章状态失败: {str(e)}")
            return False
    
    def log_publish_action(self, article_id: int, action: str, status: str, message: str = "") -> bool:
        """记录发布操作日志"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO publish_logs (article_id, action, status, message)
                    VALUES (?, ?, ?, ?)
                ''', (article_id, action, status, message))
                
                conn.commit()
                return True
                
        except Exception as e:
            logger.error(f"记录发布日志失败: {str(e)}")
            return False
    
    def get_publish_logs(self, article_id: Optional[int] = None, limit: int = 100) -> List[Dict]:
        """获取发布日志"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                if article_id:
                    cursor.execute('''
                        SELECT * FROM publish_logs WHERE article_id = ?
                        ORDER BY created_at DESC LIMIT ?
                    ''', (article_id, limit))
                else:
                    cursor.execute('''
                        SELECT pl.*, a.title as article_title
                        FROM publish_logs pl
                        LEFT JOIN articles a ON pl.article_id = a.id
                        ORDER BY pl.created_at DESC LIMIT ?
                    ''', (limit,))
                
                rows = cursor.fetchall()
                return [dict(row) for row in rows]
                
        except Exception as e:
            logger.error(f"获取发布日志失败: {str(e)}")
            return []
    
    def get_statistics(self) -> Dict:
        """获取统计信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                stats = {}
                
                # 总文章数
                cursor.execute('SELECT COUNT(*) FROM articles')
                stats['total_articles'] = cursor.fetchone()[0]
                
                # 各状态文章数
                cursor.execute('SELECT status, COUNT(*) FROM articles GROUP BY status')
                status_counts = dict(cursor.fetchall())
                stats['status_counts'] = status_counts
                
                # 平均质量分
                cursor.execute('SELECT AVG(quality_score) FROM articles WHERE quality_score > 0')
                avg_score = cursor.fetchone()[0]
                stats['average_quality_score'] = round(avg_score, 2) if avg_score else 0
                
                # 最近7天发布数
                cursor.execute('''
                    SELECT COUNT(*) FROM articles 
                    WHERE status = 'published' 
                    AND published_at >= datetime('now', '-7 days')
                ''')
                stats['recent_published'] = cursor.fetchone()[0]
                
                return stats
                
        except Exception as e:
            logger.error(f"获取统计信息失败: {str(e)}")
            return {}
    
    def search_articles(self, keyword: str, limit: int = 50) -> List[ArticleRecord]:
        """搜索文章"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                search_pattern = f'%{keyword}%'
                cursor.execute('''
                    SELECT * FROM articles 
                    WHERE title LIKE ? OR content LIKE ? OR tags LIKE ?
                    ORDER BY created_at DESC LIMIT ?
                ''', (search_pattern, search_pattern, search_pattern, limit))
                
                rows = cursor.fetchall()
                return [ArticleRecord(**dict(row)) for row in rows]
                
        except Exception as e:
            logger.error(f"搜索文章失败: {str(e)}")
            return []
