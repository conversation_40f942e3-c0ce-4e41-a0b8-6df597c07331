# Gemini PowerShell Client Launcher
param(
    [string]$Mode = "interactive",
    [string]$Message,
    [string]$Model
)

# Set console encoding to UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::InputEncoding = [System.Text.Encoding]::UTF8

Write-Host "🚀 Starting Gemini PowerShell Client..." -ForegroundColor Cyan
Write-Host ""

switch ($Mode.ToLower()) {
    "interactive" {
        Write-Host "Starting interactive chat mode..." -ForegroundColor Green
        if ($Model) {
            & "$PSScriptRoot\gemini-interactive.ps1" -Model $Model
        } else {
            & "$PSScriptRoot\gemini-interactive.ps1"
        }
    }
    "single" {
        if (-not $Message) {
            Write-Host "❌ Message required for single mode" -ForegroundColor Red
            Write-Host "Usage: .\start-gemini.ps1 -Mode single -Message 'Your question'" -ForegroundColor Yellow
            return
        }
        Write-Host "Sending single message..." -ForegroundColor Green
        if ($Model) {
            & "$PSScriptRoot\gemini-chat.ps1" -Message $Message -Model $Model
        } else {
            & "$PSScriptRoot\gemini-chat.ps1" -Message $Message
        }
    }
    "advanced" {
        Write-Host "Starting advanced client..." -ForegroundColor Green
        if ($Message) {
            & "$PSScriptRoot\gemini-advanced.ps1" -Message $Message -Model $Model
        } else {
            & "$PSScriptRoot\gemini-advanced.ps1" -Interactive -Model $Model
        }
    }
    "help" {
        Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Cyan
        Write-Host "║                    Gemini PowerShell Client                  ║" -ForegroundColor Cyan  
        Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "Available Modes:" -ForegroundColor Yellow
        Write-Host "  interactive  - Start interactive chat (default)" -ForegroundColor White
        Write-Host "  single       - Send a single message" -ForegroundColor White
        Write-Host "  advanced     - Use advanced client with config" -ForegroundColor White
        Write-Host "  help         - Show this help" -ForegroundColor White
        Write-Host ""
        Write-Host "Examples:" -ForegroundColor Yellow
        Write-Host "  .\start-gemini.ps1" -ForegroundColor Gray
        Write-Host "  .\start-gemini.ps1 -Mode interactive -Model pro" -ForegroundColor Gray
        Write-Host "  .\start-gemini.ps1 -Mode single -Message 'Hello Gemini'" -ForegroundColor Gray
        Write-Host "  .\start-gemini.ps1 -Mode advanced" -ForegroundColor Gray
        Write-Host ""
        Write-Host "Available Models:" -ForegroundColor Yellow
        Write-Host "  gemini-1.5-flash     - Fast and efficient (default)" -ForegroundColor Gray
        Write-Host "  gemini-1.5-pro       - More capable, slower" -ForegroundColor Gray
        Write-Host "  gemini-1.5-flash-8b  - Lightweight version" -ForegroundColor Gray
        Write-Host ""
    }
    default {
        Write-Host "❌ Unknown mode: $Mode" -ForegroundColor Red
        Write-Host "Use -Mode help for available options" -ForegroundColor Yellow
    }
}
