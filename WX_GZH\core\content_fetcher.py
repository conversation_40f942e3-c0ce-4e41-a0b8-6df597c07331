"""
内容抓取器模块
支持从各种平台抓取视频和文章内容
"""

import re
import requests
from typing import Dict, Optional, List
from urllib.parse import urlparse
import yt_dlp
from bs4 import BeautifulSoup
import json
from dataclasses import dataclass, field
from loguru import logger


@dataclass
class ContentInfo:
    """内容信息数据类"""
    title: str
    content: str
    author: str
    url: str
    platform: str
    content_type: str  # 'video' or 'article'
    thumbnail_url: Optional[str] = None
    duration: Optional[int] = None  # 视频时长（秒）
    publish_date: Optional[str] = None
    tags: List[str] = field(default_factory=list)
    description: Optional[str] = None


class ContentFetcher:
    """内容抓取器主类"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': config.get('user_agent', 
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
        })
        self.timeout = config.get('timeout', 30)
        self.retry_times = config.get('retry_times', 3)
        
    def fetch_content(self, url: str) -> Optional[ContentInfo]:
        """
        根据URL自动识别平台并抓取内容
        
        Args:
            url: 内容链接
            
        Returns:
            ContentInfo对象或None
        """
        try:
            platform = self._identify_platform(url)
            logger.info(f"识别平台: {platform}, URL: {url}")
            
            if platform == 'youtube':
                return self._fetch_youtube_content(url)
            elif platform == 'bilibili':
                return self._fetch_bilibili_content(url)
            elif platform == 'zhihu':
                return self._fetch_zhihu_content(url)
            elif platform == 'wechat':
                return self._fetch_wechat_content(url)
            else:
                return self._fetch_generic_article(url)
                
        except Exception as e:
            logger.error(f"抓取内容失败: {url}, 错误: {str(e)}")
            return None
    
    def _identify_platform(self, url: str) -> str:
        """识别内容平台"""
        domain = urlparse(url).netloc.lower()
        
        if 'youtube.com' in domain or 'youtu.be' in domain:
            return 'youtube'
        elif 'bilibili.com' in domain:
            return 'bilibili'
        elif 'zhihu.com' in domain:
            return 'zhihu'
        elif 'mp.weixin.qq.com' in domain:
            return 'wechat'
        else:
            return 'generic'
    
    def _fetch_youtube_content(self, url: str) -> Optional[ContentInfo]:
        """抓取YouTube视频内容"""
        try:
            ydl_opts = {
                'quiet': True,
                'no_warnings': True,
                'writesubtitles': True,
                'writeautomaticsub': True,
                'subtitleslangs': ['zh', 'zh-CN', 'en'],
                'skip_download': True,
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=False)
                
                # 提取字幕内容
                content = self._extract_subtitles(info)
                if not content:
                    content = info.get('description', '')
                
                return ContentInfo(
                    title=info.get('title', ''),
                    content=content,
                    author=info.get('uploader', ''),
                    url=url,
                    platform='YouTube',
                    content_type='video',
                    thumbnail_url=info.get('thumbnail'),
                    duration=info.get('duration'),
                    publish_date=info.get('upload_date'),
                    description=info.get('description', '')
                )
                
        except Exception as e:
            logger.error(f"抓取YouTube内容失败: {str(e)}")
            return None
    
    def _fetch_bilibili_content(self, url: str) -> Optional[ContentInfo]:
        """抓取B站视频内容"""
        try:
            # 使用yt-dlp抓取B站内容
            ydl_opts = {
                'quiet': True,
                'no_warnings': True,
                'skip_download': True,
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=False)
                
                return ContentInfo(
                    title=info.get('title', ''),
                    content=info.get('description', ''),
                    author=info.get('uploader', ''),
                    url=url,
                    platform='Bilibili',
                    content_type='video',
                    thumbnail_url=info.get('thumbnail'),
                    duration=info.get('duration'),
                    description=info.get('description', '')
                )
                
        except Exception as e:
            logger.error(f"抓取B站内容失败: {str(e)}")
            return None
    
    def _fetch_zhihu_content(self, url: str) -> Optional[ContentInfo]:
        """抓取知乎文章内容"""
        try:
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 提取标题
            title_elem = soup.find('h1', class_='Post-Title')
            if not title_elem:
                title_elem = soup.find('h1')
            title = title_elem.get_text().strip() if title_elem else ''
            
            # 提取作者
            author_elem = soup.find('meta', {'name': 'author'})
            author = author_elem.get('content', '') if author_elem else ''
            
            # 提取正文内容
            content_elem = soup.find('div', class_='Post-RichTextContainer')
            if not content_elem:
                content_elem = soup.find('div', class_='RichText')
            
            content = ''
            if content_elem:
                # 移除不需要的元素
                for elem in content_elem.find_all(['script', 'style', 'noscript']):
                    elem.decompose()
                content = content_elem.get_text().strip()
            
            return ContentInfo(
                title=title,
                content=content,
                author=author,
                url=url,
                platform='知乎',
                content_type='article'
            )
            
        except Exception as e:
            logger.error(f"抓取知乎内容失败: {str(e)}")
            return None
    
    def _fetch_wechat_content(self, url: str) -> Optional[ContentInfo]:
        """抓取微信公众号文章内容"""
        try:
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 提取标题
            title_elem = soup.find('h1', id='activity-name')
            title = title_elem.get_text().strip() if title_elem else ''
            
            # 提取作者
            author_elem = soup.find('a', class_='account_nickname_inner')
            author = author_elem.get_text().strip() if author_elem else ''
            
            # 提取正文内容
            content_elem = soup.find('div', class_='rich_media_content')
            content = ''
            if content_elem:
                # 移除不需要的元素
                for elem in content_elem.find_all(['script', 'style', 'noscript']):
                    elem.decompose()
                content = content_elem.get_text().strip()
            
            return ContentInfo(
                title=title,
                content=content,
                author=author,
                url=url,
                platform='微信公众号',
                content_type='article'
            )
            
        except Exception as e:
            logger.error(f"抓取微信公众号内容失败: {str(e)}")
            return None
    
    def _fetch_generic_article(self, url: str) -> Optional[ContentInfo]:
        """抓取通用网页文章内容"""
        try:
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 尝试提取标题
            title = ''
            title_selectors = ['h1', 'title', '[class*="title"]', '[id*="title"]']
            for selector in title_selectors:
                title_elem = soup.select_one(selector)
                if title_elem:
                    title = title_elem.get_text().strip()
                    break
            
            # 尝试提取正文内容
            content = ''
            content_selectors = [
                'article', '[class*="content"]', '[class*="article"]',
                '[id*="content"]', '[id*="article"]', 'main'
            ]
            
            for selector in content_selectors:
                content_elem = soup.select_one(selector)
                if content_elem:
                    # 移除不需要的元素
                    for elem in content_elem.find_all(['script', 'style', 'noscript', 'nav', 'header', 'footer']):
                        elem.decompose()
                    content = content_elem.get_text().strip()
                    if len(content) > 100:  # 确保内容足够长
                        break
            
            # 如果没有找到合适的内容，使用整个body
            if not content:
                body = soup.find('body')
                if body:
                    for elem in body.find_all(['script', 'style', 'noscript', 'nav', 'header', 'footer']):
                        elem.decompose()
                    content = body.get_text().strip()
            
            return ContentInfo(
                title=title,
                content=content,
                author='',
                url=url,
                platform='网页',
                content_type='article'
            )
            
        except Exception as e:
            logger.error(f"抓取网页内容失败: {str(e)}")
            return None
    
    def _extract_subtitles(self, video_info: Dict) -> str:
        """从视频信息中提取字幕内容"""
        subtitles = video_info.get('subtitles', {})
        automatic_captions = video_info.get('automatic_captions', {})
        
        # 优先使用中文字幕
        for lang in ['zh', 'zh-CN', 'zh-TW']:
            if lang in subtitles:
                return self._download_subtitle_content(subtitles[lang][0]['url'])
            if lang in automatic_captions:
                return self._download_subtitle_content(automatic_captions[lang][0]['url'])
        
        # 如果没有中文字幕，使用英文字幕
        for lang in ['en', 'en-US']:
            if lang in subtitles:
                return self._download_subtitle_content(subtitles[lang][0]['url'])
            if lang in automatic_captions:
                return self._download_subtitle_content(automatic_captions[lang][0]['url'])
        
        return ''
    
    def _download_subtitle_content(self, subtitle_url: str) -> str:
        """下载字幕内容并提取文本"""
        try:
            response = self.session.get(subtitle_url, timeout=self.timeout)
            response.raise_for_status()
            
            # 解析字幕文件（通常是VTT或SRT格式）
            content = response.text
            
            # 简单的文本提取（移除时间戳等）
            lines = content.split('\n')
            text_lines = []
            
            for line in lines:
                line = line.strip()
                # 跳过时间戳行和空行
                if (not line or 
                    line.startswith('WEBVTT') or 
                    '-->' in line or 
                    re.match(r'^\d+$', line)):
                    continue
                text_lines.append(line)
            
            return ' '.join(text_lines)
            
        except Exception as e:
            logger.error(f"下载字幕失败: {str(e)}")
            return ''
