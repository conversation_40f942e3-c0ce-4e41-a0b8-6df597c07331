param(
    [string]$ConfigFile = "gemini-config.json",
    [string]$Model,
    [string]$Message,
    [switch]$Interactive,
    [switch]$Help
)

# Set console encoding to UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::InputEncoding = [System.Text.Encoding]::UTF8

function Show-Help {
    Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Cyan
    Write-Host "║                    Gemini PowerShell Client                  ║" -ForegroundColor Cyan  
    Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Usage:" -ForegroundColor Yellow
    Write-Host "  .\gemini-advanced.ps1 -Interactive          # Start interactive chat" -ForegroundColor White
    Write-Host "  .\gemini-advanced.ps1 -Message 'Hello'      # Single message" -ForegroundColor White
    Write-Host "  .\gemini-advanced.ps1 -Model pro -Message 'Complex question'" -ForegroundColor White
    Write-Host ""
    Write-Host "Parameters:" -ForegroundColor Yellow
    Write-Host "  -Interactive    Start interactive chat mode" -ForegroundColor Gray
    Write-Host "  -Message        Send a single message" -ForegroundColor Gray
    Write-Host "  -Model          Model to use (flash, pro, flash-8b)" -ForegroundColor Gray
    Write-Host "  -ConfigFile     Path to config file (default: gemini-config.json)" -ForegroundColor Gray
    Write-Host "  -Help           Show this help" -ForegroundColor Gray
    Write-Host ""
    Write-Host "Interactive Commands:" -ForegroundColor Yellow
    Write-Host "  exit/quit       End session" -ForegroundColor Gray
    Write-Host "  clear           Clear screen" -ForegroundColor Gray
    Write-Host "  history         Show conversation history" -ForegroundColor Gray
    Write-Host "  reset           Reset conversation context" -ForegroundColor Gray
    Write-Host "  model [name]    Change or show current model" -ForegroundColor Gray
    Write-Host "  tokens          Toggle token display" -ForegroundColor Gray
    Write-Host "  save [file]     Save conversation to file" -ForegroundColor Gray
    Write-Host ""
}

if ($Help) {
    Show-Help
    return
}

# Load configuration
if (Test-Path $ConfigFile) {
    try {
        $config = Get-Content $ConfigFile -Raw | ConvertFrom-Json
        Write-Host "✅ Configuration loaded from $ConfigFile" -ForegroundColor Green
    } catch {
        Write-Host "❌ Error loading config file: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Using default configuration..." -ForegroundColor Yellow
        $config = @{
            apiKey = "AIzaSyA_zK3tYdnCf6By1cPB76h-n1WCHs4OT-I"
            defaultModel = "gemini-1.5-flash"
            models = @{
                flash = "gemini-1.5-flash"
                pro = "gemini-1.5-pro"
                "flash-8b" = "gemini-1.5-flash-8b"
            }
            settings = @{
                showTokens = $true
                maxHistoryEntries = 10
                temperature = 0.7
                maxOutputTokens = 2048
            }
        }
    }
} else {
    Write-Host "⚠️  Config file not found. Creating default config..." -ForegroundColor Yellow
    $defaultConfig = @{
        apiKey = "AIzaSyA_zK3tYdnCf6By1cPB76h-n1WCHs4OT-I"
        defaultModel = "gemini-1.5-flash"
        models = @{
            flash = "gemini-1.5-flash"
            pro = "gemini-1.5-pro"
            "flash-8b" = "gemini-1.5-flash-8b"
        }
        settings = @{
            showTokens = $true
            maxHistoryEntries = 10
            temperature = 0.7
            maxOutputTokens = 2048
            topP = 0.8
            topK = 40
        }
        colors = @{
            user = "Green"
            assistant = "Blue"
            system = "Yellow"
            error = "Red"
            info = "Cyan"
            tokens = "DarkGray"
        }
    }
    $defaultConfig | ConvertTo-Json -Depth 10 | Out-File $ConfigFile -Encoding UTF8
    $config = $defaultConfig
    Write-Host "✅ Default config created at $ConfigFile" -ForegroundColor Green
}

# Set current model
$currentModel = if ($Model -and $config.models.$Model) { 
    $config.models.$Model 
} elseif ($Model) { 
    $Model 
} else { 
    $config.defaultModel 
}

$apiKey = $config.apiKey
$uri = "https://generativelanguage.googleapis.com/v1beta/models/${currentModel}:generateContent?key=$apiKey"

# Global variables
$conversationHistory = @()
$showTokens = $config.settings.showTokens

function Send-GeminiMessage {
    param(
        [string]$Message,
        [bool]$UseHistory = $true
    )
    
    # Build conversation context
    $contents = @()
    
    if ($UseHistory) {
        # Add recent conversation history
        $recentHistory = $conversationHistory | Select-Object -Last $config.settings.maxHistoryEntries
        foreach ($entry in $recentHistory) {
            $contents += @{
                role = "user"
                parts = @(@{ text = $entry.user })
            }
            $contents += @{
                role = "model" 
                parts = @(@{ text = $entry.assistant })
            }
        }
    }
    
    # Add current message
    $contents += @{
        role = "user"
        parts = @(@{ text = $Message })
    }
    
    $requestBody = @{
        contents = $contents
        generationConfig = @{
            temperature = $config.settings.temperature
            maxOutputTokens = $config.settings.maxOutputTokens
        }
    }
    
    if ($config.settings.topP) { $requestBody.generationConfig.topP = $config.settings.topP }
    if ($config.settings.topK) { $requestBody.generationConfig.topK = $config.settings.topK }
    
    $body = $requestBody | ConvertTo-Json -Depth 10
    $body = [System.Text.Encoding]::UTF8.GetBytes($body)
    
    try {
        $response = Invoke-RestMethod -Uri $uri -Method Post -Headers @{"Content-Type"="application/json; charset=utf-8"} -Body $body
        
        if ($response.candidates -and $response.candidates.Count -gt 0) {
            $content = $response.candidates[0].content
            if ($content.parts -and $content.parts.Count -gt 0) {
                $assistantResponse = $content.parts[0].text
                
                # Add to conversation history if using history
                if ($UseHistory) {
                    $conversationHistory += @{
                        user = $Message
                        assistant = $assistantResponse
                        timestamp = Get-Date
                        model = $currentModel
                    }
                }
                
                return @{
                    success = $true
                    response = $assistantResponse
                    usage = $response.usageMetadata
                }
            }
        }
        
        return @{
            success = $false
            error = "No response content found"
        }
        
    } catch {
        return @{
            success = $false
            error = $_.Exception.Message
        }
    }
}

# Handle single message mode
if ($Message) {
    Write-Host "Sending message to $currentModel..." -ForegroundColor $config.colors.info
    Write-Host ""
    
    $result = Send-GeminiMessage -Message $Message -UseHistory $false
    
    if ($result.success) {
        Write-Host "Response:" -ForegroundColor $config.colors.assistant
        Write-Host $result.response -ForegroundColor White
        
        if ($showTokens -and $result.usage) {
            Write-Host ""
            Write-Host "💬 Tokens: $($result.usage.promptTokenCount)→$($result.usage.candidatesTokenCount) (Total: $($result.usage.totalTokenCount))" -ForegroundColor $config.colors.tokens
        }
    } else {
        Write-Host "❌ Error: $($result.error)" -ForegroundColor $config.colors.error
    }
    
    return
}

# Interactive mode (default if no message provided)
if ($Interactive -or (-not $Message)) {
    # Import interactive functions
    . "$PSScriptRoot\gemini-interactive-functions.ps1" -ErrorAction SilentlyContinue
    
    Clear-Host
    Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor $config.colors.info
    Write-Host "║                    Gemini Interactive Chat                   ║" -ForegroundColor $config.colors.info
    Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor $config.colors.info
    Write-Host ""
    Write-Host "Current Model: $currentModel" -ForegroundColor $config.colors.system
    Write-Host "Type 'help' for commands or 'exit' to quit" -ForegroundColor $config.colors.system
    Write-Host ""
    
    # Interactive loop will be implemented in the next part due to length constraints
    Write-Host "Interactive mode starting..." -ForegroundColor $config.colors.info
    Write-Host "Use .\gemini-interactive.ps1 for full interactive experience" -ForegroundColor $config.colors.system
}
