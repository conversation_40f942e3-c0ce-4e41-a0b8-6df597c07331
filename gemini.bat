@echo off
chcp 65001 >nul
cd /d "%~dp0"

if "%1"=="help" (
    echo.
    echo ================================================================
    echo                    Gemini PowerShell Client
    echo ================================================================
    echo.
    echo Usage:
    echo   gemini                    - Start interactive chat
    echo   gemini "Your message"     - Send single message
    echo   gemini help               - Show this help
    echo.
    echo Examples:
    echo   gemini
    echo   gemini "What is AI?"
    echo   gemini "Translate 'Hello' to Chinese"
    echo.
    goto :eof
)

if "%1"=="" (
    echo Starting Gemini Interactive Chat...
    powershell -ExecutionPolicy Bypass -File "gemini-interactive.ps1"
) else (
    echo Sending message to Gemini...
    powershell -ExecutionPolicy Bypass -File "gemini-chat.ps1" -Message "%*"
)
