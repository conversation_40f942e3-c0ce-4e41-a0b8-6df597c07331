"""
AI 提示词模板配置
用于不同类型的内容分析和生成任务
"""

# 内容分析提示词模板
CONTENT_ANALYSIS_PROMPT = """
请分析以下内容，并按照要求生成一篇适合微信公众号发布的文章：

原始内容类型：{content_type}
原始内容：
{content}

要求：
1. 提取核心观点和要点
2. 加入你的理解和见解，提供独特的视角
3. 使用适合微信公众号的语言风格（轻松、有趣、易懂）
4. 结构清晰，逻辑性强
5. 字数控制在{min_length}-{max_length}字
6. 包含引人入胜的开头和有力的结尾

请按以下格式输出：

标题：[生成一个吸引人的标题]

摘要：[100字以内的文章摘要]

正文：
[文章正文内容，包含：
- 引人入胜的开头
- 核心观点分析
- 个人见解和思考
- 实用的建议或启发
- 有力的结尾]

标签：[3-5个相关标签，用逗号分隔]

配图建议：[建议的配图类型和描述]
"""

# 标题生成提示词模板
TITLE_GENERATION_PROMPT = """
基于以下文章内容，生成5个不同风格的微信公众号标题：

文章摘要：{summary}
核心关键词：{keywords}

要求：
1. 标题要吸引人，激发读者点击欲望
2. 长度控制在15-25个字
3. 包含不同风格：疑问式、数字式、对比式、情感式、实用式
4. 避免标题党，确保与内容相符

请按以下格式输出：
1. [疑问式标题]
2. [数字式标题]  
3. [对比式标题]
4. [情感式标题]
5. [实用式标题]

推荐标题：[从上述5个中选择最佳的一个]
"""

# 内容重写提示词模板
CONTENT_REWRITE_PROMPT = """
请对以下文章内容进行重写和优化：

原文：
{original_content}

优化要求：
1. 保持原文的核心观点和信息
2. 改善语言表达，使其更加流畅自然
3. 调整段落结构，提高可读性
4. 增加适当的过渡句和连接词
5. 确保语调符合微信公众号风格
6. 字数控制在{target_length}字左右

请输出优化后的文章：
"""

# 观点生成提示词模板
OPINION_GENERATION_PROMPT = """
基于以下内容，生成个人观点和见解：

原始内容：{content}
内容主题：{topic}

请从以下角度提供观点：
1. 个人理解和解读
2. 实际应用和启发
3. 可能的争议点或不同看法
4. 对读者的建议或思考方向
5. 与当前热点或趋势的关联

要求：
- 观点要有深度和独特性
- 语言要亲和力强，贴近读者
- 避免过于学术化的表达
- 每个观点100-200字

请按以下格式输出：

个人解读：
[你对这个内容的理解和解读]

实用启发：
[读者可以从中获得的实际启发]

深度思考：
[更深层次的思考和分析]

读者建议：
[给读者的具体建议或行动指南]
"""

# 摘要生成提示词模板
SUMMARY_GENERATION_PROMPT = """
请为以下内容生成一个简洁有力的摘要：

内容：{content}

要求：
1. 字数控制在80-120字
2. 突出核心观点
3. 语言简洁明了
4. 能够激发读者继续阅读的兴趣
5. 适合作为微信公众号文章的摘要

摘要：
"""

# 标签生成提示词模板
TAG_GENERATION_PROMPT = """
基于以下文章内容，生成相关标签：

标题：{title}
摘要：{summary}
主要内容：{content}

要求：
1. 生成5-8个相关标签
2. 包含主题标签、情感标签、类型标签
3. 标签要准确反映文章内容
4. 适合微信公众号的标签风格

请按以下格式输出：
主题标签：[2-3个核心主题标签]
类型标签：[1-2个内容类型标签，如：观点、分享、教程等]
情感标签：[1-2个情感相关标签，如：励志、思考、有趣等]
"""

# 内容质量检查提示词模板
QUALITY_CHECK_PROMPT = """
请对以下文章进行质量检查和评估：

标题：{title}
正文：{content}

检查项目：
1. 内容原创性和独特性
2. 逻辑结构是否清晰
3. 语言表达是否流畅
4. 是否符合微信公众号风格
5. 是否包含敏感或不当内容
6. 标题与内容是否匹配

请按以下格式输出评估结果：

质量评分：[1-10分]

优点：
- [列出文章的优点]

需要改进的地方：
- [列出需要改进的具体问题]

修改建议：
- [提供具体的修改建议]

发布建议：[建议发布/需要修改后发布/不建议发布]
"""

# 提示词模板映射
PROMPT_TEMPLATES = {
    "content_analysis": CONTENT_ANALYSIS_PROMPT,
    "title_generation": TITLE_GENERATION_PROMPT,
    "content_rewrite": CONTENT_REWRITE_PROMPT,
    "opinion_generation": OPINION_GENERATION_PROMPT,
    "summary_generation": SUMMARY_GENERATION_PROMPT,
    "tag_generation": TAG_GENERATION_PROMPT,
    "quality_check": QUALITY_CHECK_PROMPT,
}

def get_prompt_template(template_name: str) -> str:
    """获取指定的提示词模板"""
    return PROMPT_TEMPLATES.get(template_name, "")

def format_prompt(template_name: str, **kwargs) -> str:
    """格式化提示词模板"""
    template = get_prompt_template(template_name)
    if not template:
        raise ValueError(f"Unknown template: {template_name}")
    
    try:
        return template.format(**kwargs)
    except KeyError as e:
        raise ValueError(f"Missing required parameter for template {template_name}: {e}")
