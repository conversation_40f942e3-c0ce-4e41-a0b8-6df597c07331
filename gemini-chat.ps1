param(
    [Parameter(Mandatory=$true)]
    [string]$Message,
    [string]$ApiKey = "AIzaSyA_zK3tYdnCf6By1cPB76h-n1WCHs4OT-I",
    [string]$Model = "gemini-1.5-flash"
)

# Set console encoding to UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::InputEncoding = [System.Text.Encoding]::UTF8

# Model aliases
$modelMap = @{
    "flash" = "gemini-1.5-flash"
    "pro" = "gemini-1.5-pro"
    "flash-8b" = "gemini-1.5-flash-8b"
}

# Resolve model name
$resolvedModel = if ($modelMap.ContainsKey($Model)) { $modelMap[$Model] } else { $Model }

$uri = "https://generativelanguage.googleapis.com/v1beta/models/${resolvedModel}:generateContent?key=$ApiKey"

Write-Host "🤖 Using model: $resolvedModel" -ForegroundColor Cyan
Write-Host ""

$body = @{
    contents = @(
        @{
            parts = @(
                @{
                    text = $Message
                }
            )
        }
    )
} | ConvertTo-Json -Depth 10

# Ensure UTF-8 encoding
$body = [System.Text.Encoding]::UTF8.GetBytes($body)

try {
    Write-Host "🤔 Thinking..." -ForegroundColor Yellow
    $response = Invoke-RestMethod -Uri $uri -Method Post -Headers @{"Content-Type"="application/json; charset=utf-8"} -Body $body

    if ($response.candidates -and $response.candidates.Count -gt 0) {
        $content = $response.candidates[0].content
        if ($content.parts -and $content.parts.Count -gt 0) {
            Write-Host "`r✨ Gemini Response:" -ForegroundColor Green
            Write-Host ""
            Write-Host $content.parts[0].text -ForegroundColor White
        } else {
            Write-Host "`r❌ No response content found" -ForegroundColor Yellow
        }
    } else {
        Write-Host "`r❌ No candidates found in response" -ForegroundColor Yellow
    }

    if ($response.usageMetadata) {
        Write-Host ""
        Write-Host "💬 Token Usage:" -ForegroundColor Cyan
        Write-Host "   Input: $($response.usageMetadata.promptTokenCount) tokens" -ForegroundColor Gray
        Write-Host "   Output: $($response.usageMetadata.candidatesTokenCount) tokens" -ForegroundColor Gray
        Write-Host "   Total: $($response.usageMetadata.totalTokenCount) tokens" -ForegroundColor Gray
    }

} catch {
    Write-Host "`r❌ Error calling Gemini API: $($_.Exception.Message)" -ForegroundColor Red
}
