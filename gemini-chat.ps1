param(
    [Parameter(Mandatory=$true)]
    [string]$Message,
    [string]$ApiKey = "AIzaSyA_zK3tYdnCf6By1cPB76h-n1WCHs4OT-I"
)

$uri = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=$ApiKey"

$body = @{
    contents = @(
        @{
            parts = @(
                @{
                    text = $Message
                }
            )
        }
    )
} | ConvertTo-Json -Depth 10

# Ensure UTF-8 encoding
$body = [System.Text.Encoding]::UTF8.GetBytes($body)

try {
    $response = Invoke-RestMethod -Uri $uri -Method Post -Headers @{"Content-Type"="application/json; charset=utf-8"} -Body $body
    
    if ($response.candidates -and $response.candidates.Count -gt 0) {
        $content = $response.candidates[0].content
        if ($content.parts -and $content.parts.Count -gt 0) {
            Write-Host "Gemini Response:" -ForegroundColor Green
            Write-Host $content.parts[0].text
        } else {
            Write-Host "No response content found" -ForegroundColor Yellow
        }
    } else {
        Write-Host "No candidates found in response" -ForegroundColor Yellow
    }
    
    Write-Host "`nUsage:" -ForegroundColor Cyan
    Write-Host "Input tokens: $($response.usageMetadata.promptTokenCount)"
    Write-Host "Output tokens: $($response.usageMetadata.candidatesTokenCount)"
    Write-Host "Total tokens: $($response.usageMetadata.totalTokenCount)"
    
} catch {
    Write-Host "Error calling Gemini API: $($_.Exception.Message)" -ForegroundColor Red
}
